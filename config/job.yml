job:

  # 扫描步进 单位为marlin所支持的最小粒度
  step_distance:
    x: 1
    y: 1
    z: 1

  # 工作区域 [X,Y,Z]
  working_area:
    start: [0,0,0]
    end:   [0,0,0]

  # 扫描模式
  # - z_only   只扫描 Z
  # - xy_only  只扫描 XY (平面扫描)
  # - xyz      XYZ扫描 (立体扫描)
  scan_mode: "z_only"

  # 优先轴
  # - x X优先(仅 xy_only/xyz 模式有效， 且xyz模式下只影响每一个平面的轴顺序)
  # - y Y优先(仅 xy_only/xyz 模式有效， 且xyz模式下只影响每一个平面的轴顺序)
  first_axis: "x"

  # 行进方式
  # - zigzag Z形
  # - snake 蛇形
  planner_path:
    x: "zigzag"
    y: "zigzag"
    z: "zigzag"

  # 总循环次数
  repeat: 1

  trig_attr:
    # 单点拍照次数
    repeat: 1
    # 触发释放前延时
    post_delay_ms: 0
    # 触发释放时间
    active_duration_ms: 10
    # 触发释放后延迟
    after_delay_ms: 0
