joystick:
  enable: true
  path: "/dev/input/event0"
  name: "Microsoft X-Box 360 pad"

control:
  step_distance: 0
  bindings:
    BTN_A:
      name: "A"
      bind_func: 0
    BTN_B:
      name: "B"
      bind_func: 0
    BTN_X:
      name: "X"
      bind_func: 0
    BTN_Y:
      name: "Y"
      bind_func: 0
    ABS_LY:
      name: "方向键上下"
      bind_func: 0
    ABS_LX:
      name: "方向键左右"
      bind_func: 0
    ABS_X:
      name: "左摇杆上下"
      bind_func: 0
    ABS_X:
      name: "左摇杆左右"
      bind_func: 0
    BTN_THUMBL:
      name: "左摇杆确认按钮"
      bind_func: 0
    ABS_RX:
      name: "右摇杆上下"
      bind_func: 0
    ABS_RX_LEFT:
      name: "右摇杆左右"
      bind_func: 0
    BTN_THUMBR:
      name: "右摇杆确认按钮"
      bind_func: 0
    BTN_START:
      name: "START"
      bind_func: 0
    BTN_BACK:
      name: "BACK"
      bind_func: 0
    BTN_LB:
      name: "LB"
      bind_func: 0
    BTN_RB:
      name: "RB"
      bind_func: 0
    BTN_MODE:
      name: "MODE"
      bind_func: 0
