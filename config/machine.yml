serial:
  device: /dev/ttyACM0
  baudrate: 115200
  use_simulate: true

axis:
  absolute: true       # false: relative positioning
  home: [0, 0, 0]      # homming position

  x:
    g-name: "X"        # gcode driver name
    driver:
      name: "tmc2209"  # driver name
      current: 2000    # stepper current
      microsteps: 8    # microstep
      ratio: 16        # 1mm to step
      feed: 100        # stepper feedrate
      backlash: 0      # belt backlash
  
  y:
    g-name: "Y"
    driver:
      name: "tmc2209"  # driver name
      current: 2000    # stepper current
      microsteps: 8    # microstep
      ratio:      16   # 1mm to step
      feed:       100  # stepper feedrate
      backlash: 0      # belt backlash

  z:
    g-name: "Z"
    driver:
      name: "tmc2209"  # driver name
      current: 2000    # stepper current
      microsteps: 256  # microstep
      ratio:      16   # 1mm to step
      feed:       100  # stepper feedrate
      backlash: 0      # belt backlash

gpio:
  cam_trigger: 17

gcode:
  init:
    - G21
    - ${machine.axis.absolute?G90:G91}
    - M154 S1   # enable report
    - M92 X${machine.axis.x.driver.ratio} #
    - M92 Y${machine.axis.y.driver.ratio} #
    - M92 Z${machine.axis.x.driver.ratio} #
    - M245 X${machine.axis.x.driver.backlash} # x backlash distance
    - M245 Y${machine.axis.y.driver.backlash} # y backlash distance
    - M245 Z${machine.axis.z.driver.backlash} # z backlash distance
    - M569 S1 X # StealthChop mode
    - M569 S1 Y # StealthChop mode
    - M569 S1 Z # StealthChop mode
    - M906 X${machine.axis.x.driver.current} # x current
    - M906 Y${machine.axis.y.driver.current} # y current
    - M906 Z${machine.axis.z.driver.current} # z current
    - M17 # enable stepper power
