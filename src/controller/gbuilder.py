from src.config import config
from src.controller.geval import geval

class gbuilder:
    
    _list: list[str] = []
    _geval: geval = None

    def __init__(self):
        self._geval = geval()
        pass

    def reg_domain(self, domain: str, ref: dict):
        self._geval.reg_domain(domain, ref)
        return self
    
    def add(self, cmd: str):
        self._list.append(self._geval.eval(cmd))
        return self

    # def add(self, cmd: str, params: dict):
    #     return self

    def build(self) -> list[str]:
        return self._list

    def parse(cfg: config, cmds: list[str]) -> "gbuilder":
        _ctx = gbuilder()
        _ctx.reg_domain("machine", cfg.machine)
        _ctx.reg_domain("joystick", cfg.joystick)
        _ctx.reg_domain("server", cfg.server)
        _ctx.reg_domain("job", cfg.job)

        for _cmd in cmds:
            _ctx.add(_cmd)
        return _ctx
