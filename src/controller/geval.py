
class token:
    TOKEN_EVAL = '$'
    TOKEN_EVAL_BRACKET_L = '{'
    TOKEN_EVAL_BRACKET_R = "}"

class geval:

    _list: list[str] = []
    _domain: dict = {}

    def reg_domain(self, domain: str, ref: dict):
        self._domain[domain] = ref

    def __eval_expression(self, expr: str) -> str:

        # simplest implementation
        if "?" in expr:
            _cond, _sel = expr.split("?")
            _true, _false = _sel.split(":")
            return self.__eval_condition(_cond, _true, _false)

        # domain access
        if "." in expr:
            return self.__eval_domain_access(expr)

        return expr

    def __eval_condition(self, cond: str, a: str, b: str) -> str:

        _cond_val = ""
        _is_domain_access_only = False
        if "==" not in cond or \
            "!=" not in cond or \
            ">" not in cond or \
            "<" not in cond:
                _is_domain_access_only = True
                _cond_val = self.__get_domain_data(cond)
        
        if _is_domain_access_only:
            if not _cond_val:
                return b
            if isinstance(_cond_val, str):
                if _cond_val == "True":
                    return a
                if _cond_val == "False":
                    return b
                return a
            

    def __eval_domain_access(self, domain: str):
        return self.__get_domain_data(domain)

    def __get_domain_data(self, path: str) -> str:
        _keys = path.split('.')
        _value = self._domain
        for _k in _keys:
            if _value is None:
                return None

            if isinstance(_value, dict):
                _value = _value.get(_k)
                if isinstance(_value, list):
                    continue
            if isinstance(_value, list):
                for _i in _value:
                    if isinstance(_i, dict):
                        _value = _i.get(_k)
                        continue

        if _value is not None:
            return f"{_value}"
        
        return _value

    def eval(self, cmd: str) -> str:

        # select expression
        # ${condition?A:B} => emit A if condition is true, else B

        # format expression
        # ${domain.A} => get value from domain

        _result = ""
        _expr_current = ""
        _is_start_expr = False
        _is_got_bracket_l = False

        for _ch in cmd:

            match _ch:
                case token.TOKEN_EVAL:
                    _is_start_expr = True
                    continue

                case token.TOKEN_EVAL_BRACKET_L:
                    if not _is_start_expr or _is_got_bracket_l:
                        raise ValueError(f"invalid token `{_ch}`")
                    _is_got_bracket_l = True
                    continue

                case token.TOKEN_EVAL_BRACKET_R:
                    if not _is_start_expr or not _is_got_bracket_l:
                        raise ValueError(f"invalid token `{_ch}`")
                    # print(_expr_current)
                    _result += self.__eval_expression(_expr_current)
                    _expr_current = ""
                    _is_start_expr = False
                    _is_got_bracket_l = False
                    continue

                case _:
                    if not _is_start_expr:
                        _result += _ch
                    else:
                        _expr_current += _ch
                    continue
        
        if _is_start_expr:
            raise ValueError(f"unexpected eof `{token.TOKEN_EVAL_BRACKET_R}`")

        # print(cmd, " | ", _result)
        return _result
