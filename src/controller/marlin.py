import os
import serial
import threading
import queue
import asyncio
import time
from dataclasses import dataclass
from typing import NamedTuple
from src.service import service
from src.controller.gbuilder import gbuilder
from src.controller.simulate.serial import Serial as FakeSerial

@dataclass
class cmd_task:
    cmd: str
    result: bool = False
    done: bool = False

@dataclass
class connector:
    queue: queue
    on_cmd_dequeue: callable
    on_cmd_send: callable
    on_cmd_sent: callable
    on_queue_refill: callable
    on_queue_empty: callable

@dataclass
class marlin_position:
    x: float
    y: float
    z: float
    e: float
    x_um: int
    y_um: int
    z_um: int
    e_um: int
    count_x: int
    count_y: int
    count_z: int
        
class marlinctl:

    _ctx: service = None
    _dev: str = None
    _serial: serial = None
    _baudrate: int = None
    _read_timeout: int = 0
    _write_timeout: int = 0
    _use_simulate: bool = False

    _workthread: threading.Thread = None
    _threadctl: False
    _threadbind: connector = None
    # _threadqueue: queue = None
    _threadbind_internal: queue = None
    _current_cmd: cmd_task = None

    is_ready: bool = False
    position: marlin_position = None
    version: str = None

    def __init__(self, ctx, dev, baudrate, read_timeout = 5,
                 write_timeout = 1, simulate = False):

        self._ctx = ctx

        # serial config
        self._dev = dev
        self._baudrate = baudrate
        self._read_timeout = read_timeout
        self._write_timeout = write_timeout
        self._use_simulate = simulate
        if simulate:
            self.version = "Pandapy DEBUG (v1.0)"

        if not os.path.exists(dev) and not simulate:
            self._ctx.log_fatal(f"serial device `{dev}` does not exist")
            return

        self.position = marlin_position(
            x = 0.0,
            y = 0.0,
            z = 0.0,
            e = 0.0,
            x_um = 0,
            y_um = 0,
            z_um = 0,
            e_um = 0,
            count_x = 0,
            count_y = 0,
            count_z = 0,
        )

        self.is_ready = simulate

        # prepare the internal bind arguments
        def __on_cmd_dequeue(conn:connector) -> cmd_task:
            if conn.queue.empty():
                return None
            return conn.queue.get(timeout = 0)

        # def __on_cmd_send(conn:connector, cmd:cmd_task) -> bool:
        #     return True

        # def __on_cmd_sent(conn:connector, cmd:cmd_task) -> cmd_task:
        #     pass

        self._threadbind_internal = connector(
            queue=queue.Queue(),
            on_cmd_dequeue=__on_cmd_dequeue,
            on_cmd_send=None,
            on_cmd_sent=None,
            on_queue_refill=None,
            on_queue_empty=None
        )

        # start thread
        self._threadctl = False
        self._threadbind = self._threadbind_internal
        self._workthread = threading.Thread(target=self.__work_thread, daemon=True)
        self._workthread.start()
        pass

    def __work_thread(self):
        try:
            if self._use_simulate:
                self._serial = FakeSerial(
                    port = self._dev,
                    baudrate = self._baudrate,
                    timeout = self._read_timeout,
                    write_timeout = self._write_timeout,
                    dsrdtr = True
                )
            else:
                self._serial = serial.Serial(
                    port = self._dev,
                    baudrate = self._baudrate,
                    timeout = self._read_timeout,        # 读操作的超时时间（秒）
                    write_timeout = self._write_timeout, # 写操作的超时时间（秒）
                    dsrdtr = True,                       # 设备重置
                )

            self._serial.reset_input_buffer()  # 清空接收缓冲区
            self._serial.reset_output_buffer() # 清空发送缓冲区
            self._ctx.log_info(f"opening serial device `{self._dev}`")

        except serial.SerialException as e:
            self._ctx.log_fatal(f"failed to open serial device `{self._dev}`: {e}")
            return

        while not self._threadctl:

            def __process_send():

                # pick up command from queue
                if self.is_ready and self._current_cmd is None:
                    try:

                        # dequeue a command task
                        self._current_cmd = self._threadbind.on_cmd_dequeue(self._threadbind)
                        if not self._current_cmd:

                            # time.sleep(0.1)

                            # if the queue is empty, refill
                            if self._threadbind.on_queue_refill:
                                if not self._threadbind.on_queue_refill(self._threadbind):
                                    if self._threadbind.queue.empty():
                                        self._threadbind.on_queue_empty(self._threadbind)
                            return

                        self._ctx.log_debug(f"[{self._threadbind.queue.qsize()}] {self._current_cmd}")

                        # before sending command
                        if self._threadbind.on_cmd_send:
                            if not self._threadbind.on_cmd_send(self._threadbind, self._current_cmd):
                                self._ctx.log_debug(f"skipping comment: {self._current_cmd.cmd}")
                                self._current_cmd.done = True
                                self._current_cmd.result = True
                                self._current_cmd = None
                                return

                        if self._current_cmd.cmd.startswith(";"):
                            # if the command is a comment, skip it
                            self._ctx.log_debug(f"skipping comment: {self._current_cmd.cmd}")
                            self._current_cmd.done = True
                            self._current_cmd.result = True
                            self._current_cmd = None
                        else:
                            # send command to serial
                            self._serial.write(f"{self._current_cmd.cmd}\n".encode('utf-8'))

                            # after sending command
                            if self._threadbind.on_cmd_sent:
                                self._threadbind.on_cmd_sent(self._threadbind, self._current_cmd)

                            self._ctx.log_debug(f"<{self._current_cmd.cmd}")
                    except Exception as e:
                        self._ctx.log_fatal(f"failed to get command from queue: {e}")
                        self._current_cmd = None
                        return
            def __process_read():

                # read serial data
                try:

                    # 检查是否有数据等待读取
                    if not self._serial.in_waiting > 0:
                        return
                    line = self._serial.readline().decode('utf-8').strip()
                    if not line:
                        return

                    self._ctx.log_debug(f">{line}")

                    match (line):
                        case "ok":
                            if self._current_cmd:
                                self._current_cmd.done = True
                                self._current_cmd.result = True
                                self._current_cmd = None
                            return
                        case "echo:busy:":
                            self._ctx.log_warning(f"waiting for command, device is busy.")
                            return
                        case "echo:Unknown command:":
                            self._ctx.log_warning(f"device is not supported this command.")
                            if self._current_cmd:
                                self._current_cmd.done = True
                                self._current_cmd.result = False
                                self._current_cmd = None
                            return

                        # report format
                        # X:0.00 Y:0.00 Z:0.00 E:0.00 Count X:0 Y:0 Z:0
                        case _ if line.startswith("X:"):
                            _parts = line.split(" ")

                            # parse position
                            self.position.x = float(_parts[0].split(":")[1])
                            self.position.y = float(_parts[1].split(":")[1])
                            self.position.z = float(_parts[2].split(":")[1])
                            self.position.e = float(_parts[3].split(":")[1])
                            self.position.x_um = int(self.position.x * 1000)
                            self.position.y_um = int(self.position.y * 1000)
                            self.position.z_um = int(self.position.z * 1000)
                            self.position.e_um = int(self.position.e * 1000)
                            self.position.count_x = int(_parts[5].split(":")[1])
                            self.position.count_y = int(_parts[6].split(":")[1])
                            self.position.count_z = int(_parts[7].split(":")[1])

                        # reoport firmwre version
                        case _ if line.startswith("Marlin"):
                            self.version = line.split(" ")[1]
                            self.is_ready = True
                            return

                except serial.SerialException as e:
                    self._ctx.log_fatal(f"serial error: {e}")
                    return

            __process_send()
            __process_read()
            time.sleep(0.001)

        self._serial.close()
        self._ctx.log_info(f"serial device `{self._dev}` closed")
    
    def wait_init(self):
        start_time = time.time()
        self._ctx.log_debug("waiting for serial device to be ready...")
        while not self.is_ready:
            time.sleep(0.5)
            if time.time() - start_time > 10:
                self._ctx.log_fatal("waiting for serial device timed out")
                return False
        return True

    def get_firmware(self) -> str:
        return self.version

    def write_cmd(self, cmd: str, wait = True) -> bool:
        task = cmd_task(cmd = cmd)
        self._threadbind_internal.queue.put(task)
    
        while wait and not task.done:
            time.sleep(0.1)
            continue

        return task.result

    def write_gbuilder(self, builder: gbuilder):
        for _cmd in builder.build():
            self.write_cmd(_cmd, wait = False)
        pass

    def bind_queue(self, bindarg: connector):
        self._threadbind = bindarg
        pass

    def unbind_queue(self):
        self._threadbind = self._threadbind_internal
        pass

    def get_queue(self) -> queue.Queue:
        return self._threadbind_internal.queue

    def emergency_halt(self):
        self._serial.close()
        self.__init__() # reopen serial cause the DTR signal reset the board
        pass

    def stop(self):
        self._threadctl = True
        self._workthread.join()
        pass
