import os
import threading
import queue
import time

class Serial:

    _wrotes: queue = None

    def __init__(self, port, baudrate, timeout, write_timeout, dsrdtr):
        self._wrotes = queue.Queue()
        pass

    def reset_input_buffer(self):
        print("Input buffer reset")
        pass

    def reset_output_buffer(self):
        print("Output buffer reset")
        pass

    def readline(self):
        wroteline: str = "\n"

        if not self._wrotes.empty():
            wroteline = self._wrotes.get()
        else:
            time.sleep(0.1)
            return b""

        command = wroteline.split(" ")
        handled = False
        if command[0].startswith("G"):
            handled = True
            if command[0] == "G4":
                time.sleep(float(command[1].removeprefix("P")) / 1000.0)
        elif command[0].startswith("M"):
            handled = True
        elif command[0].startswith("J"):
            handled = True

        if handled:
            return b"ok\n"
        else:
            return b"echo:Unknown command:"

    def close(self):
        print("Closing serial connection")
        pass

    def write(self, data):
        self._wrotes.put(data.decode('utf-8').strip())
        pass
