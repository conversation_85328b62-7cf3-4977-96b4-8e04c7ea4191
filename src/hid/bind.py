
class constants:
    NONE            = 0      # 无
    SET_PATH_START  = 0x100  # 设定包围起点
    SET_PATH_END    = 0x101  # 设定包围终点
    SWAP_PATH_POINT = 0x102  # 交换包围点
    START_JOB       = 0x103  # 开始工作
    DECREASE_SPEED  = 0x104  # 减速
    INCREASE_SPEED  = 0x105  # 加速
    STOP_JOB        = 0x106  # 停止工作
    MOVE_X          = 0x107  # X轴移动
    MOVE_Y          = 0x109  # Y轴移动
    MOVE_Z          = 0x10B  # Z轴移动
    EMERGENCY_STOP  = 0x10D  # 紧急停止
