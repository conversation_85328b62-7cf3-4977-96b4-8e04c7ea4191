from evdev import resolve_ecodes_dict

class controller_device:

    def __init__(self):
        self.__event_list = []

    def is_compatible(self, dev):
        pass
    
    def push_controller_event(self, name, value):
        self.push_event((name, value))
    
    def push_event(self, event):
        self.__event_list.append(event)

    def get_events(self):
        return self.__event_list

    def clean_events(self):
        self.__event_list = []

    def evdev_event_to_controller_event(self, evdev_e):
        pass

    def get_button_name(self, ecodes_key, event_code):
        for name in resolve_ecodes_dict(({ecodes_key: [event_code]})):
            name_a = name[1][0][0]
            if isinstance(name_a, list):
                return name[1][0][0][0]
            else:
                return name[1][0][0]

    