from .controller_device import controller_device
from evdev import ecodes

class keyboard(controller_device):
    def is_compatible(self, dev):
        caps = dev.capabilities()
        if ecodes.EV_KEY not in caps:
            return False
        keys = caps[ecodes.EV_KEY]
        keyboard_keys = [
            ecodes.KEY_A,
            ecodes.KEY_Z,
            ecodes.KEY_ENTER,
            ecodes.KEY_ESC,
            ecodes.KEY_LEFTSHIFT,
        ]
        return any(k in keys for k in keyboard_keys)

    def evdev_event_to_controller_event(self, evdev_e):
        if evdev_e.type == ecodes.EV_KEY:
            name = self.get_button_name(evdev_e.type, evdev_e.code)
            value = evdev_e.value if evdev_e.value < 1 else 1
            self.push_controller_event(name, value)
            return None
        elif evdev_e.type == ecodes.EV_SYN:
            e = self.get_events()
            self.clean_events()
            return e

        return None