from .controller_device import controller_device
from evdev import ecodes

class xbox_controller(controller_device):
    __btn = {
        308: "BTN_Y",
        307: "BTN_X",
        304: "BTN_A",
        305: "BTN_B",
        318: "BTN_THUMBR",
        317: "BTN_THUMBL",
        314: "BTN_BACK",
        315: "BTN_START",
        316: "BTN_MODE",
        310: "BTN_LB",
        311: "BTN_RB",
    }

    __abs = {
        0: "ABS_Y", #up: -32768 down: 32511 
        1: "ABS_X", #left: -32512 right: 32767 
        2: "ABS_LT", 
        5: "ABS_RT",
        16: "ABS_LX",
        17: "ABS_LY",
        3: "ABS_RX",
        4: "ABS_RY"
    }

    __abs_meta = {}


    def is_compatible(self, dev):
        caps = dev.capabilities()
        if dev.name in ["Microsoft X-Box 360 pad"] and ecodes.EV_ABS in caps:
            self.__get_abs_capabilities(dev)
            return True
        return False

    def __get_abs_capabilities(self, dev):
        caps = dev.capabilities()
        self.__abs_meta = {}
        for i in caps[3]:
            item = i
            id = item[0]
            absinfo = item[1]
            self.__abs_meta[id] = absinfo
        return self.__abs_meta

    def __get_button_name(self, evdev_e):
        try:
            if evdev_e.type == ecodes.EV_KEY:
                return self.__btn[evdev_e.code]
            elif evdev_e.type == ecodes.EV_ABS:
                return self.__abs[evdev_e.code]
        except:
            return self.get_button_name(evdev_e.type, evdev_e.code)

    def __normalize_abs(self, val, absinfo):
        # 特殊的归一化，区间在[-1,1]，保留负数部分
        # print(val, absinfo, type(absinfo.max))
        if val == 0:
            return 0
        
        # 获取实际的范围
        min_val = absinfo.min
        max_val = absinfo.max
        
        # 防止除零和异常情况
        if max_val == 0 and min_val == 0:
            return 0
        
        if val > 0:
            return float(val) / max_val if max_val != 0 else 0
        else:  # val < 0
            # 使用绝对值确保正确的归一化
            return float(val) / abs(min_val) if min_val != 0 else 0

    def evdev_event_to_controller_event(self, evdev_e):
        if evdev_e.type == ecodes.EV_KEY:
            name = self.__get_button_name(evdev_e)
            value = evdev_e.value if evdev_e.value < 1 else 1
            self.push_controller_event(name, value)
            return None
        elif evdev_e.type == ecodes.EV_ABS:
            # print(evdev_e)
            name = self.__get_button_name(evdev_e)
            value = self.__normalize_abs(evdev_e.value, self.__abs_meta[evdev_e.code])
            if value < 0.2 and value > -0.2:
                value = 0
            self.push_controller_event(name, value)
        elif evdev_e.type == ecodes.EV_SYN:
            e = self.get_events()
            self.clean_events()
            return e