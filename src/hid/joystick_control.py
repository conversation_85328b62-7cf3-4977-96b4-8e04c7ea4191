from src.service import code, service
from src.hid.watcher import hidmon
from src.job.status import status
from src.hid.bind import constants
from src.controller.service import controller

class joystick_control(service):
    def __init__(self, context):
        super().__init__(context)
        self._hidmon = None
        self._callback_id = None
        self._controller = None  # controller服务实例

        # 摇杆状态
        self._analog_x = 32768  # 中心位置
        self._analog_y = 32768  # 中心位置
        self._analog_z = 32768  # 中心位置

        self._on_3d_control = False

        # 摇杆映射配置
        self._joystick_mapping_config = None

    def init(self):
        self._hidmon = self.get_service(hidmon)
        if not self._hidmon:
            return code.DEFERRED

        # 获取controller服务
        self._controller = self.get_service(controller)
        if not self._controller:
            return code.DEFERRED

        # 加载摇杆映射配置
        self._load_joystick_mapping_config()

        self._callback_id = self._hidmon.register_controller_event(self._on_controller_event)
        return code.SUCCESS

    def start(self):
        return code.SUCCESS

    def stop(self):
        if self._callback_id and self._hidmon:
            self._hidmon.unregister_event(self._callback_id)

        # 停止所有重复定时器
        self._stop_all_repeat_timers()
        return code.SUCCESS

    def get_name(self):
        return "joystick-3d-control"

    def get_status(self):
        return code.SUCCESS

    def isEnable(self):
        from src.job.manager import manager
        mgrsrv = self.get_service(manager)
        if not mgrsrv:
            return False
            
        runner = mgrsrv.get_job_runner()
        if runner is None:
            return False
        
        if runner.get_job_status() == status.STOP:
            return True

        return False

    def _on_controller_event(self, events):
        if not self.isEnable():
            print("not enable")
            return
        
        if not events:
            print("not events")
            return
            
        self._load_joystick_mapping_config()
        _on_3d_control = False
        for event_name, value in events:
            # axis = self._get_axis_mapping(event_name)
            bind_func = self._joystick_mapping_config.get(event_name, 0)["bind_func"]
            if bind_func == constants.NONE:
                continue
            elif bind_func == constants.MOVE_X:
                v = self._normalize_to_range(value)
                if v != self._analog_x:
                    self._analog_x = self._normalize_to_range(value)
                    _on_3d_control = True
            elif bind_func == constants.MOVE_Y:
                v = self._normalize_to_range(value)
                if v != self._analog_y:
                    self._analog_y = self._normalize_to_range(value)
                    _on_3d_control = True
            elif bind_func == constants.MOVE_Z:
                v = self._normalize_to_range(value)
                if v != self._analog_z:
                    self._analog_z = self._normalize_to_range(value)
                    _on_3d_control = True
            elif bind_func == constants.SET_PATH_START:
                if value > 0:  # 按键按下
                    self._handle_set_path_start()
            elif bind_func == constants.SET_PATH_END:
                if value > 0:  # 按键按下
                    self._handle_set_path_end()
            elif bind_func == constants.SWAP_PATH_POINT:
                if value > 0:  # 按键按下
                    self._handle_swap_path_point()
            elif bind_func == constants.START_JOB:
                if value > 0:  # 按键按下
                    self._handle_start_job()
            elif bind_func == constants.DECREASE_SPEED:
                if value > 0:  # 按键按下
                    self._handle_decrease_speed()
            elif bind_func == constants.INCREASE_SPEED:
                if value > 0:  # 按键按下
                    self._handle_increase_speed()
            elif bind_func == constants.STOP_JOB:
                if value > 0:  # 按键按下
                    self._handle_stop_job()
            elif bind_func == constants.EMERGENCY_STOP:
                if value > 0:  # 按键按下
                    self._handle_emergency_stop()


        if _on_3d_control:
            gcode = self.get_3d_control_gcode()
            self._send_gcode(gcode)
            # print(f"发送gcode: {gcode}")
            
    def _normalize_to_range(self, value):
        if value == 0:
            return int(32768)
        normalized = (value + 1.0) / 2.0
        return int(normalized * 65536)

    def _load_joystick_mapping_config(self):
        config = self.get_config()
        if config and hasattr(config, 'joystick'):
            bindings = config.joystick.get("control", {}).get("bindings", {})
            self._joystick_mapping_config = bindings
        else:
            self._joystick_mapping_config = {}

    def get_3d_control_gcode(self):
        return "J8 {} {} {}".format(self._analog_x, self._analog_y, self._analog_z)

    def _send_gcode(self, gcode_command):
        """发送gcode命令到marlin控制器"""
        if not self._controller:
            self.log_warning("Controller服务未初始化，无法发送gcode")
            return False

        try:
            marlin = self._controller.get_marlin()
            # 使用write_cmd方法发送gcode，wait=False表示异步发送
            marlin.write_cmd(gcode_command, wait=False)
            # self.log_debug(f"成功发送gcode命令: {gcode_command}")
            return True
        except Exception as e:
            self.log_fatal(f"发送gcode命令失败: {e}")
            return False

    def _get_job_runner(self):
        """获取job runner实例"""
        from src.job.manager import manager
        mgrsrv = self.get_service(manager)
        if not mgrsrv:
            return None
        return mgrsrv.get_job_runner()

    def _handle_set_path_start(self):
        """设定包围起点"""
        try:
            runner = self._get_job_runner()
            if not runner:
                self.log_warning("Job manager未初始化")
                return

            if runner.is_running():
                self.log_warning("作业正在运行，无法设置起点")
                return

            runner.set_scan_start()
            runner.save_config()
            self.log_info("已设置扫描起点为当前位置")
        except Exception as e:
            self.log_fatal(f"设置扫描起点失败: {e}")

    def _handle_set_path_end(self):
        """设定包围终点"""
        try:
            runner = self._get_job_runner()
            if not runner:
                self.log_warning("Job manager未初始化")
                return

            if runner.is_running():
                self.log_warning("作业正在运行，无法设置终点")
                return

            runner.set_scan_end()
            runner.save_config()
            self.log_info("已设置扫描终点为当前位置")
        except Exception as e:
            self.log_fatal(f"设置扫描终点失败: {e}")

    def _handle_swap_path_point(self):
        """交换包围点"""
        try:
            runner = self._get_job_runner()
            if not runner:
                self.log_warning("Job manager未初始化")
                return

            if runner.is_running():
                self.log_warning("作业正在运行，无法交换路径点")
                return

            runner.swap_working_area()
            runner.save_config()
            self.log_info("已交换扫描起点和终点")
        except Exception as e:
            self.log_fatal(f"交换路径点失败: {e}")

    def _handle_start_job(self):
        """开始工作"""
        try:
            runner = self._get_job_runner()
            if not runner:
                self.log_warning("Job manager未初始化")
                return

            if runner.is_running():
                self.log_warning("作业已在运行")
                return

            runner.start()
            self.log_info("已开始作业")
        except Exception as e:
            self.log_fatal(f"开始作业失败: {e}")

    def _handle_stop_job(self):
        """停止工作"""
        try:
            runner = self._get_job_runner()
            if not runner:
                self.log_warning("Job manager未初始化")
                return

            if not runner.is_running():
                self.log_warning("作业未在运行")
                return

            runner.stop()
            self.log_info("已停止作业")
        except Exception as e:
            self.log_fatal(f"停止作业失败: {e}")

    def _handle_emergency_stop(self):
        """紧急停止"""
        try:
            # 紧急停止marlin控制器
            if self._controller:
                marlin = self._controller.get_marlin()
                marlin.emergency_halt()
                self.log_info("已执行紧急停止")

            # 同时停止作业
            runner = self._get_job_runner()
            if runner and runner.is_running():
                runner.stop()
                self.log_info("已停止作业")
        except Exception as e:
            self.log_fatal(f"紧急停止失败: {e}")

    def _handle_decrease_speed(self):
        """减速"""
        try:
            # 发送减速gcode命令 (降低进给速度)
            self._send_gcode("M220 S90")  # 设置进给速度为90%
            self.log_info("已降低进给速度至90%")
        except Exception as e:
            self.log_fatal(f"减速失败: {e}")

    def _handle_increase_speed(self):
        """加速"""
        try:
            # 发送加速gcode命令 (提高进给速度)
            self._send_gcode("M220 S110")  # 设置进给速度为110%
            self.log_info("已提高进给速度至110%")
        except Exception as e:
            self.log_fatal(f"加速失败: {e}")