from src.service import code, service
from src.hid.watcher import hidmon
from src.job.status import status
from src.hid.bind import constants

class joystick_control(service):    
    def __init__(self, context):
        super().__init__(context)
        self._hidmon = None
        self._callback_id = None
        
        # 摇杆状态
        self._analog_x = 32768  # 中心位置
        self._analog_y = 32768  # 中心位置  
        self._analog_z = 32768  # 中心位置

        self._on_3d_control = False
        
        # 摇杆映射配置
        self._joystick_mapping_config = None

    def init(self):
        self._hidmon = self.get_service(hidmon)
        if not self._hidmon:
            return code.DEFERRED
        
        # 加载摇杆映射配置
        self._load_joystick_mapping_config()
        
        self._callback_id = self._hidmon.register_controller_event(self._on_controller_event)
        return code.SUCCESS

    def start(self):
        return code.SUCCESS

    def stop(self):
        if self._callback_id and self._hidmon:
            self._hidmon.unregister_event(self._callback_id)
        return code.SUCCESS

    def get_name(self):
        return "joystick-3d-control"

    def get_status(self):
        return code.SUCCESS

    def isEnable(self):
        from src.job.manager import manager
        mgrsrv = self.get_service(manager)
        if not mgrsrv:
            return False
            
        runner = mgrsrv.get_job_runner()
        if runner is None:
            return False
        
        if runner.get_job_status() == status.STOP:
            return True

        return False

    def _on_controller_event(self, events):
        print(events)
        if not self.isEnable():
            print("not enable")
            return
        
        if not events:
            print("not events")
            return
            
        self._load_joystick_mapping_config()
        _on_3d_control = False
        for event_name, value in events:
            # axis = self._get_axis_mapping(event_name)
            bind_func = self._joystick_mapping_config.get(event_name, 0)["bind_func"]
            if bind_func == constants.NONE:
                continue
            elif bind_func == constants.MOVE_X:
                v = self._normalize_to_range(value)
                if v != self._analog_x:
                    self._analog_x = self._normalize_to_range(value)
                    _on_3d_control = True
            elif bind_func == constants.MOVE_Y:
                v = self._normalize_to_range(value)
                if v != self._analog_y:
                    self._analog_y = self._normalize_to_range(value)
                    _on_3d_control = True
            elif bind_func == constants.MOVE_Z:
                v = self._normalize_to_range(value)
                if v != self._analog_z:
                    self._analog_z = self._normalize_to_range(value)
                    _on_3d_control = True
            elif bind_func == constants.SET_PATH_START:
                pass
            elif bind_func == constants.SET_PATH_END:
                pass
            elif bind_func == constants.SWAP_PATH_POINT:
                pass
            elif bind_func == constants.START_JOB:
                pass
            elif bind_func == constants.DECREASE_SPEED:
                pass
            elif bind_func == constants.INCREASE_SPEED:
                pass
            elif bind_func == constants.STOP_JOB:
                pass
            elif bind_func == constants.EMERGENCY_STOP:
                pass


        if _on_3d_control:
            gcode = self.get_3d_control_gcode() # TODO塞进队列
            print(gcode)
            
    def _normalize_to_range(self, value):
        if value == 0:
            return int(32768)
        normalized = (value + 1.0) / 2.0
        return int(normalized * 65536)

    def _load_joystick_mapping_config(self):
        config = self.get_config()
        if config and hasattr(config, 'joystick'):
            bindings = config.joystick.get("control", {}).get("bindings", {})
            self._joystick_mapping_config = bindings
        else:
            self._joystick_mapping_config = {}

    def get_3d_control_gcode(self):
        return "j8 {} {} {}".format(self._analog_x, self._analog_y, self._analog_z)