from src.service import code
from src.service import service
import asyncio
from typing import Callable, Dict, List
import time
from evdev import InputDevice, categorize, ecodes, list_devices, resolve_ecodes_dict
# import src.hid.hid_utils
from src.hid import hid_utils
from .device import get_hid_compatible_device, controller_device
import uuid

# TODO: 需要给/dev/input/event*的权限(chmod 666)，或者使用root

class hidmon(service):
    dev = None
    key_callbacks: List[Callable] = {}
    generic_callbacks: List[Callable] = {}
    controller_event_callbacks: List[Callable] = {}
    controller_define_dev: controller_device = None

    def get_conf(self):
        return self.context.get_config().joystick["joystick"]

    def init(self):
        conf = self.get_conf()
        if conf:
            if conf["enable"]:
                self.open(conf["path"], conf["name"])
                self.context.log_info(f"Opened HID device: {conf['name']}, {conf['path']}")
        return code.SUCCESS

    def get_name(self):
        return "hidmon"

    def start(self):
        self.context.log_info("Starting HID monitor")
        # TODO: Hot plug
        while True:
            if self.dev:
                try:
                    asyncio.run(self.read_async())
                except:
                    time.sleep(1)
            else:
                time.sleep(1)
        return super().start()
    
    def get_device_list(self):
        devices = [InputDevice(path) for path in list_devices()]
        # print("Available HID devices:")
        for dev in devices:
            caps = dev.capabilities()
            # print(f"{dev.path}: {dev.name}") #{caps} {self.match_device(dev)}
        return devices
    
    def get_device_list_json(self):
        devices = self.get_device_list()
        return [{"path": dev.path, "name": dev.name} for dev in devices]
    
    def open(self, path, name):
        devices = self.get_device_list()
        for dev in devices:
            if dev.name == name and dev.path == path:
                self.dev = dev
                self.controller_define_dev = self.match_device(dev)
                return code.SUCCESS
        return code.FAILURE
    
    def close(self):
        self.dev.close()
        return code.SUCCESS

    def reset(self):
        self.close()
        conf = self.get_conf()
        if conf:
            if conf["enable"]:
                self.open(conf["path"], conf["name"])
                self.context.log_info(f"Opened HID device: {conf['name']}, {conf['path']}")
    
    async def read_async(self):
        while True:
            for event in self.dev.read_loop():
                name = hid_utils.get_button_name(event.type, event.code)
                # print(event, name)
                for callback in self.generic_callbacks:
                    self.generic_callbacks[callback](event)
                if event.type == ecodes.EV_KEY:
                    # print(event, name)
                    for callback in self.key_callbacks:
                        self.generic_callbacks[callback](event)
                elif event.type == ecodes.EV_ABS:
                    pass
                if self.controller_define_dev :
                    e = self.controller_define_dev.evdev_event_to_controller_event(event)
                    print(e)
                    if e:
                        # print(self.controller_event_callbacks)
                        for callback in self.controller_event_callbacks:
                            self.controller_event_callbacks[callback](e)

    def register_key_event(self, callback: Callable):
        uuid = hid_utils.get_uuid()
        self.key_callbacks[uuid] = callback
        return uuid

    def register_any_event(self, callback: Callable):
        uuid = hid_utils.get_uuid()
        self.generic_callbacks[uuid] = callback
        return uuid

    def register_controller_event(self, callback: Callable):
        uuid = hid_utils.get_uuid()
        self.controller_event_callbacks[uuid] = callback

    def unregister_event(self, uuid: str):
        if uuid in self.key_callbacks:
            del self.key_callbacks[uuid]
            return code.SUCCESS
        if uuid in self.generic_callbacks:
            del self.generic_callbacks[uuid]
            return code.SUCCESS
        if uuid in self.controller_event_callbacks:
            del self.controller_event_callbacks[uuid]
            return code.SUCCESS
        return code.FAILURE

    # 匹配设备
    def match_device(self, device: InputDevice):
        device_compatible_list = get_hid_compatible_device()
        for device_compatible in device_compatible_list:
            d = device_compatible()
            if d.is_compatible(device):
                self.log_info(f"Found compatible device: {device.name}")
                return d
        
        return None