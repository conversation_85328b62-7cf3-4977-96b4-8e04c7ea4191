# THIS FILE IS GENERATED AUTOMATICALLY
# DO NOT EDIT DIRECTLY.

pid $NGINX_WORKDIR/nginx.pid;
error_log $NGINX_WORKDIR/nginx_error.log warn;

events {
    worker_connections 1024;
}

http {
    access_log $NGINX_WORKDIR/nginx_access.log;
    
    server {
        listen $NGINX_PORT;
        listen [::]:$NGINX_PORT;

        # nginx hosting the static site data
        # location / {
        #     root $BACKEND_WWW_STATIC;
        #     index index.html;
        # }

        # FUCK CROS CNMMP
        location / {
            proxy_pass http://localhost:5888/;
            # WebSocket headers
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # Optional: Increase timeout for WebSocket connections
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }

        # reverse proxy /api/* to backend
        location ^~ /api/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_pass http://localhost:$BACKEND_PORT/;
        }
    }
}
