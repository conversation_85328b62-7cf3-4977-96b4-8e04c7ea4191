import src
import os
import serial.tools.list_ports as serial_ports
import src.hid.bind as bind
from src.http.api import httpapi
from src.config import config
from src.utils import utils

def __save_config(ctx:httpapi, conf:config):
    conf_sysconf_path = os.path.join(utils.get_workdir(), "config")
    conf.dump(conf_sysconf_path, item={"machine", "joystick", "server", "system"})
    return 0, True

def getCfgItems(ctx: httpapi, search, post_data):
    keys = list(ctx.service.get_config().machine.keys())
    return 0, {"keys": keys}

def getLocalSerialDevices(ctx: httpapi, search, post_data):
    ports = serial_ports.comports()
    devices = [{"device": port.device, "name": port.name} for port in ports]
    # remove empty device
    devices = [dev for dev in devices if dev["name"]]
    return 0, {"devices": devices}

def getSerialConfig(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    serial_conf = conf.machine["serial"]
    return 0, {
        "serialPort": serial_conf["device"],
        "baudRate": serial_conf["baudrate"],
    }

def setSerialConfig(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    if "serialPort" in post_data:
        conf.machine["serial"]["device"] = post_data["serialPort"]
    if "baudRate" in post_data:
        conf.machine["serial"]["baudrate"] = post_data["baudRate"]
    return 0, __save_config(ctx, conf)

def getJoystickKeyBindings(ctx: httpapi, searchs, post_data):
    conf:config = ctx.service.get_config()
    bindings = conf.joystick["control"]["bindings"]
    bindings = [{"name": name, "option": option} for name, option in bindings.items()]
    return 0, {"bindings": bindings}

def setJoystickKeyBindings(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    if "bindings" in post_data:
        for bind in post_data["bindings"]:
            if conf.joystick["control"]["bindings"].get(bind):
                conf.joystick["control"]["bindings"][bind]["bind_func"] = post_data["bindings"][bind]
    return 0, __save_config(ctx, conf)

def getJoystickKeyFunctionList(ctx: httpapi, search, post_data):
    return 0, {"functions": [
        {"name": "无", "value": bind.constants.NONE},
        {"name": "设定包围起点", "value": bind.constants.SET_PATH_START},
        {"name": "设定包围终点", "value": bind.constants.SET_PATH_END},
        {"name": "交换包围点", "value": bind.constants.SWAP_PATH_POINT},
        {"name": "开始工作", "value": bind.constants.START_JOB},
        {"name": "行进速度增加", "value": bind.constants.DECREASE_SPEED},
        {"name": "行进速度减少", "value": bind.constants.INCREASE_SPEED},
        {"name": "停止工作", "value": bind.constants.STOP_JOB},
        {"name": "X轻移 (加)", "value": bind.constants.MOVE_X_POSITIVE},
        {"name": "X轻移 (减)", "value": bind.constants.MOVE_X_NEGATIVE},
        {"name": "Y轻移 (加)", "value": bind.constants.MOVE_Y_POSITIVE},
        {"name": "Y轻移 (减)", "value": bind.constants.MOVE_Y_NEGATIVE},
        {"name": "Z轻移 (加)", "value": bind.constants.MOVE_Z_POSITIVE},
        {"name": "Z轻移 (减)", "value": bind.constants.MOVE_Z_NEGATIVE},
        {"name": "急停", "value": bind.constants.EMERGENCY_STOP},
    ]}

def getInputDeviceList(ctx: httpapi, search, post_data):
    hidmon = ctx.service.get_service(src.hid.watcher.hidmon)
    devices = hidmon.get_device_list_json()
    return 0, {"devices": devices}

def getInputDevice(ctx: httpapi, search, post_data):
    conf = ctx.service.get_config().joystick["joystick"]
    return 0, {
        "joystickDevice": conf["path"],
    }

def setInputDevice(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    if "joystickDevice" in post_data:
        conf.joystick["joystick"]["path"] = post_data["joystickDevice"]
        # 可以在这里添加设备名称的设置逻辑
        # 通过路径找到对应的设备名称
        hidmon = ctx.service.get_service(src.hid.watcher.hidmon)
        devices = hidmon.get_device_list_json()
        for device in devices:
            if device["path"] == post_data["joystickDevice"]:
                conf.joystick["joystick"]["name"] = device["name"]
                break
        return 0, __save_config(ctx, conf)
    return 0, False

def getInitialGCode(ctx, search, post_data):
    conf:config = ctx.service.get_config()
    return 0, {"gcode": conf.machine["gcode"]["init"]}

def setInitialGCode(ctx, search, post_data):
    conf:config = ctx.service.get_config()
    if "gcode" in post_data:
        conf.machine["gcode"]["init"] = post_data["gcode"].split("\n")
    return 0, __save_config(ctx, conf)

def getStepperArgs(ctx:httpapi, search, post_data):
    conf:config = ctx.service.get_config()

    def __get_stepper_args(axis):
        return {
            "current": axis["current"],
            "microsteps": axis["microsteps"],
            "ratio": axis["ratio"],
            "feedrate": axis["feed"],
            # "acceleration": axis["acceleration"],
        }

    _args = {
        "x": __get_stepper_args(conf.machine["axis"]["x"]["driver"]),
        "y": __get_stepper_args(conf.machine["axis"]["y"]["driver"]),
        "z": __get_stepper_args(conf.machine["axis"]["z"]["driver"]),
    }
    return 0, _args

def setStepperArgs(ctx:httpapi, search, post_data):
    conf:config = ctx.service.get_config()

    # {"xMicrostep":10,"yMicrostep":8,"zMicrostep":256,"xStepperAmps":2000,"yStepperAmps":2000,"zStepperAmps":2000,"xStepperFeedrate":100,"yStepperFeedrate":100,"zStepperFeedrate":100,"xStepperRatio":16,"yStepperRatio":16,"zStepperRatio":16}
    
    if "xMicrostep" in post_data:
        conf.machine["axis"]["x"]["driver"]["microsteps"] = post_data["xMicrostep"]
    if "yMicrostep" in post_data:
        conf.machine["axis"]["y"]["driver"]["microsteps"] = post_data["yMicrostep"]
    if "zMicrostep" in post_data:
        conf.machine["axis"]["z"]["driver"]["microsteps"] = post_data["zMicrostep"]
    if "xStepperAmps" in post_data:
        conf.machine["axis"]["x"]["driver"]["current"] = post_data["xStepperAmps"]
    if "yStepperAmps" in post_data:
        conf.machine["axis"]["y"]["driver"]["current"] = post_data["yStepperAmps"]
    if "zStepperAmps" in post_data:
        conf.machine["axis"]["z"]["driver"]["current"] = post_data["zStepperAmps"]
    if "xStepperFeedrate" in post_data:
        conf.machine["axis"]["x"]["driver"]["feed"] = post_data["xStepperFeedrate"]
    if "yStepperFeedrate" in post_data:
        conf.machine["axis"]["y"]["driver"]["feed"] = post_data["yStepperFeedrate"]
    if "zStepperFeedrate" in post_data:
        conf.machine["axis"]["z"]["driver"]["feed"] = post_data["zStepperFeedrate"]
    if "xStepperRatio" in post_data:
        conf.machine["axis"]["x"]["driver"]["ratio"] = post_data["xStepperRatio"]
    if "yStepperRatio" in post_data:
        conf.machine["axis"]["y"]["driver"]["ratio"] = post_data["yStepperRatio"]
    if "zStepperRatio" in post_data:
        conf.machine["axis"]["z"]["driver"]["ratio"] = post_data["zStepperRatio"]

    return 0, __save_config(ctx, conf)

def getGPIOBinding(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    return 0, {
        "cameraTriggerGpio": conf.machine["gpio"]["cam_trigger"]
    }

def setGPIOBinding(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    if "cameraTriggerGpio" in post_data:
        conf.machine["gpio"]["cam_trigger"] = post_data["cameraTriggerGpio"]
        return 0, __save_config(ctx, conf)
    return 0, False

def getNotificationSettings(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    return 0, {
        "finishSound": conf.system["notification"]["on_finish"],
        "filmSound": conf.system["notification"]["on_picture"],
    }

def setNotificationSettings(ctx: httpapi, search, post_data):
    conf:config = ctx.service.get_config()
    if "finishSound" in post_data:
        conf.system["notification"]["on_finish"] = post_data["finishSound"]
    if "filmSound" in post_data:
        conf.system["notification"]["on_picture"] = post_data["filmSound"]
    return 0, __save_config(ctx, conf)
