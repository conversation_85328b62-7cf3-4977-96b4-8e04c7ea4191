name: 'Close stale issues'

on:
  schedule:
    - cron: '0 1 * * *'

jobs:
  stale:
    if: github.repository == 'vbenjs/vue-vben-admin'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'This issue is stale because it has been open 60 days with no activity. Remove stale label or comment or this will be closed in 7 days'
          stale-pr-message: 'This PR is stale because it has been open 60 days with no activity. Remove stale label or comment or this will be closed in 7 days'
          exempt-issue-labels: 'bug,enhancement'
          days-before-stale: 60
          days-before-close: 7
