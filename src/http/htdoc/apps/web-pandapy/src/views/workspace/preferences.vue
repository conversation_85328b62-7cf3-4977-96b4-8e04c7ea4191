<template>
  <Page v-if="jobLoaded" title="偏好设置" description="本页面用于设定当前工程的参数">
    <NCard title="拍照参数"><FilmForm /></NCard><br/>
    <NCard title="路径规划器"><PlannerForm /></NCard>
  </Page>

  <div v-else class="non-loaded">
    <span>工程尚未加载</span>
    <span>请先打开或创建一个工程文件</span>
  </div>

</template>

<style lang="scss" scoped>

.non-loaded {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    justify-content: center;

  span {
    margin-bottom: 0.5rem;
    font-size: small;
    opacity: 0.5;
  }
}
</style>

<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { NButton, NCard, useMessage } from 'naive-ui';
import { useVbenForm } from '#/adapter/form';
import { setJobPrefrences, getJobPrefrences, getCurrentJob } from '#/api/index';
import { ref } from 'vue';

const message = useMessage();
const jobLoaded = ref(false);
getCurrentJob().then(r => {

  if(r == null) {
    jobLoaded.value = false;
    return;
  }

  jobLoaded.value = true;
  getJobPrefrences('plannerArgs').then((res) => {
    plannerFormApi.setValues(res);
  });

  getJobPrefrences('filmArgs').then((res) => {
    filmFormApi.setValues(res);
  });

});

const [FilmForm, filmFormApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    setJobPrefrences('filmArgs',values).then(() => {
      message.success('设置已保存');
    }).catch((error) => {
      message.error(`保存失败: ${error.message}`);
    });
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: "InputNumber",
      fieldName: "repeat",
      label: "循环次数",
      componentProps: {
        min: 1,
      },
    },
    {
      component: "InputNumber",
      fieldName: "filmCount",
      label: "单点拍照次数",
      componentProps: {
        min: 1,
      },
    },
    {
      component: "InputNumber",
      fieldName: "beforeFilmDelay",
      label: "触发前等待时间",
      componentProps: {
        min: 0
      },
    },
    {
      component: "InputNumber",
      fieldName: "afterFilmDelay",
      label: "触发后等待时间",
      componentProps: {
        min: 0
      },
    },
    {
      component: "InputNumber",
      fieldName: "filmDuration",
      label: "拍照触发时长",
      componentProps: {
        min: 0
      },
    },
  ],
});

const [PlannerForm, plannerFormApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {
    setJobPrefrences('plannerArgs',values).then(() => {
      message.success('设置已保存');
    }).catch((error) => {
      message.error(`保存失败: ${error.message}`);
    });
  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: "Select",
      fieldName: "scanMode",
      label: "扫描模式",
      componentProps: {
        options: [
          { label: '垂直扫描 (Z 轴)', value: 'z_only' },
          { label: '平面扫描 (XY 轴)', value: 'xy_only' },
          { label: '立体扫描 (XYZ 轴)', value: 'xyz' },
        ],
      },
    },
    {
      component: "Divider",
      fieldName: "preferredAxisDivider",
    },
    {
      component: 'RadioGroup',
      fieldName: 'preferredAxis',
      label: '扫描优先轴',
      componentProps: {
        isButton: true,
        class: 'flex flex-wrap',
        options: [
          { value: 'x', label: 'X 轴优先' },
          { value: 'y', label: 'Y 轴优先' },
        ],
      },
      dependencies: {
        disabled(values) {
          return values.scanMode == 'z_only';
        },
        triggerFields: ['scanMode'],
      },
    },
    // {
    //   component: 'RadioGroup',
    //   fieldName: 'unitSelect',
    //   label: '使用单位',
    //   componentProps: {
    //     isButton: true,
    //     class: 'flex flex-wrap',
    //     options: [
    //       { value: 'unit-um', label: '微米(um)' },
    //       { value: 'unit-mm', label: '毫米(mm)' },
    //       { value: 'unit-cm', label: '厘米(mm)' },
    //     ],
    //   },
    // },
    {
      component: 'RadioGroup',
      fieldName: 'xPlannerPath',
      label: 'X',
      componentProps: {
        isButton: true,
        class: 'flex flex-wrap',
        options: [
          { value: 'zigzag', label: 'Zig-Zag' },
          { value: 'snake', label: '蛇形' },
        ],
      },
      dependencies: {
        triggerFields: ['scanMode', 'preferredAxis'],
        disabled(values) {
          return values.scanMode == 'z_only' || values.preferredAxis == 'y';
        },
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'yPlannerPath',
      label: 'Y',
      componentProps: {
        isButton: true,
        class: 'flex flex-wrap',
        options: [
          { value: 'zigzag', label: 'Zig-Zag' },
          { value: 'snake', label: '蛇形' },
        ],
      },
      dependencies: {
        triggerFields: ['scanMode', 'preferredAxis'],
        disabled(values) {
          return values.scanMode == 'z_only' || values.preferredAxis == 'x';
        },
      },
    },
    {
      component: 'RadioGroup',
      fieldName: 'zPlannerPath',
      label: 'Z',
      componentProps: {
        isButton: true,
        class: 'flex flex-wrap',
        options: [
          { value: 'zigzag', label: 'Zig-Zag' },
          { value: 'snake', label: '蛇形' },
        ],
      },
      dependencies: {
        triggerFields: ['scanMode'],
        disabled(values) {
          return values.scanMode == 'z_only' || values.scanMode == 'xy_only';
        },
      },
    },
    {
      component: "Divider",
      fieldName: "scanStepDivider",
    },
    {
      component: "RadioGroup",
      fieldName: "xScanStepUnit",
      label: "X 参数",
      componentProps: {
        isButton: true,
        options: [
          { label: '微米(um)', value: 'um' },
          { label: '毫米(mm)', value: 'mm' },
          { label: '厘米(cm)', value: 'cm' },
        ],
      },
    },
    {
      component: "InputNumber",
      fieldName: "scanXStep",
      label: "每步长度",
      componentProps: {
        min: 0
      },
    },
    {
      component: "RadioGroup",
      fieldName: "yScanStepUnit",
      label: "Y 参数",
      componentProps: {
        isButton: true,
        options: [
          { label: '微米(um)', value: 'um' },
          { label: '毫米(mm)', value: 'mm' },
          { label: '厘米(cm)', value: 'cm' },
        ],
      },
    },
    {
      component: "InputNumber",
      fieldName: "scanYStep",
      label: "每步长度",
      componentProps: {
        min: 0
      },
    },
    {
      component: "RadioGroup",
      fieldName: "zScanStepUnit",
      label: "Z 参数",
      componentProps: {
        isButton: true,
        options: [
          { label: '微米(um)', value: 'um' },
          { label: '毫米(mm)', value: 'mm' },
          { label: '厘米(cm)', value: 'cm' },
        ],
      },
    },
    {
      component: "InputNumber",
      fieldName: "scanZStep",
      label: "每步长度",
      componentProps: {
        min: 0
      },
    },
  ],
});

</script>
