import os
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor
from src.config import config
from src.job.status import status
from src.job.planner import planner
from src.job.util.timecalc import timecalc
from src.controller.service import controller
from src.controller.marlin import marlinctl
from src.controller.marlin import marlin_position
from src.controller.marlin import connector
from src.controller.marlin import cmd_task

class runner:

    _manager = None
    _marlin: marlinctl = None

    _is_running: bool = False
    _current_job: str = None
    _job_config: config = None
    _job_dir: str = None
    _job_status: status = status.STOP
    _job_planner: planner = None
    _job_planner_bind: connector = None
    
    # status control
    _stat_repeat: int = 0
    _stat_step: int = 0
    _stat_remain: timecalc = None
    # _stat_last_step_time: int = 0
    # _stat_step_duration: int = 0

    def __init__(self, manager, job_dir: str):
        self._manager = manager
        self._job_dir = job_dir
        self._is_running = False
        self._current_job = None
        self._job_executor = ThreadPoolExecutor(max_workers=4)
        self._stat_remain = timecalc()

        ctrlsrv = self._manager.get_service(controller)
        self._marlin = ctrlsrv.get_marlin()

    def is_running(self) -> bool:
        return self._is_running

    def get_job_message(self) -> str:

        def format_sec(sec: int) -> str:
            if sec < 60:
                return f"{int(sec)} 秒"
            elif sec < 3600:
                return f"{int(sec // 60)} 分 {int(sec % 60)} 秒"
            else:
                return f"{int(sec // 3600)} 小时 {int(sec % 3600 // 60)} 分 {int(sec % 60)} 秒"

        if self._job_status == status.RUNNING or self._job_status == status.PAUSED:
            total_steps = self._job_planner.get_total_steps()
            total_repeat = self._job_config.job["job"]["repeat"]
            message = f"总共 {total_steps} 张，第 {self._stat_step} 张 (第 {self._stat_repeat}/{total_repeat} 次循环) "
            message += f"预计剩余 {format_sec((total_steps - self._stat_step) * self._stat_remain.get_average_duration() / 1000)}"
            return message
        
        elif self._job_status == status.ERROR or self._job_status == status.STOP:

            def fmt_um_to_mm_array(arr: list) -> str:
                return f"[{', '.join([str(round(x / 1000, 3)) for x in arr])}]"

            return f"开始位置xyz {fmt_um_to_mm_array(self._job_config.job['job']['working_area']['start'])}, \
                     结束位置xyz {fmt_um_to_mm_array(self._job_config.job['job']['working_area']['end'])}"

        elif self._job_status == status.PLANNING:
            return "正在规划作业路径"
        else:
            return "当前没有正在运行的作业"

    def reset(self):
        self._is_running = False
        self._current_job = None
        self._job_config = None
        self._job_status = status.STOP
        self._job_planner = None
        self._job_planner_itor_stopped = False
        self._job_planner_itor = None
        self._job_progress = 0.0
        self._job_planner_lock = None
        self._stat_remain.reset()

    def get_job_status(self) -> status:
        return self._job_status
    
    def get_job_progress(self) -> float:
        return self._job_progress if self._is_running else 0.0

    def get_job_name(self) -> str:
        return self._current_job
    
    def set_job_name(self, name) -> str:
        self._current_job = name

    def __set_job_progress(self, progress: float):
        if progress < 0 or progress > 100:
            return
        self._job_progress = progress

    def start(self, from_resume:bool = False):

        if from_resume and self._is_running:
            self._manager.log_info("Resuming job from previous state")

            # bind the planner queue to the marlin controller
            self._marlin.bind_queue(self._job_planner_bind)
            self._job_status = status.RUNNING
            return
        
        self._is_running = True
        self._job_status = status.PLANNING
        self._job_progress = 0.0

        # plan the route
        try:
            self._job_planner = planner(self._job_config)
            self._job_planner.prepare()
            self._job_planner_itor = self._job_planner.export_gcode()
            self._job_planner_itor_stopped = False
            self._job_planner_lock = threading.Lock()
            self._job_status = status.RUNNING

        except Exception as e:
            self._manager.log_fatal(f"Failed to plan job: {e}")
            self._job_status = status.ERROR
            self._is_running = False
            self._job_planner = None
            raise ValueError("路径规划出现问题, 请稍后再试")

        def __on_cmd_dequeue(conn:connector) -> cmd_task:
            if conn.queue.empty():
                return None
            return conn.queue.get(timeout = 0)

        def __on_cmd_send(conn:connector, cmd:cmd_task):
            if cmd.cmd.startswith("; PROGRESS:"):
                try:
                    progress = float(cmd.cmd.split(" ")[-1])
                    self.__set_job_progress(round(progress * 100, 1))
                    return False
                except ValueError:
                    self._manager.log_fatal(f"Invalid progress value in command: {cmd.cmd}")
                    return False
            elif cmd.cmd.startswith("; REPEAT:"):
                try:
                    self._stat_repeat = int(cmd.cmd.split(" ")[-1]) + 1
                    return False
                except ValueError:
                    self._manager.log_fatal(f"Invalid repeat value in command: {cmd.cmd}")
                    return False

            elif cmd.cmd.startswith("; STEP:"):
                try:
                    self._stat_step = int(cmd.cmd.split(" ")[-1])
                    self._stat_remain.feed()
                    return False
                except ValueError as e:
                    self._manager.log_fatal(f"Invalid repeat value in command: {cmd.cmd} {e}")
                    return False
                
            elif cmd.cmd.startswith("; PLAYSND:"):
                try:
                    sound_type = cmd.cmd.split(" ")[-1]
                    if sound_type == "done":
                        pass
                    elif sound_type == "film":
                        pass
                    return False
                except ValueError as e:
                    self._manager.log_fatal(f"Invalid repeat value in command: {cmd.cmd} {e}")
                    return False
            return True

        def __on_queue_refill(conn:connector):
            def __do_refill():
                try:
                    with self._job_planner_lock:
                        for i in range(100):
                            conn.queue.put(cmd_task(cmd = next(self._job_planner_itor)))
                    return True
                except StopIteration:
                    self._manager.log_info("Job planner has no more commands to process")
                    self._job_planner_itor_stopped = True
                    return False

            if self._job_planner_itor_stopped:
                return False

            self._job_executor.submit(__do_refill)
            return True

        def __on_queue_empty(conn:connector):
            self._marlin.unbind_queue()
            self._job_status = status.STOP
            self._is_running = False
            self._manager.log_info("Job completed successfully")

        # bind the planner queue to the marlin controller
        self._job_planner_bind = connector(
            queue=queue.Queue(),
            on_cmd_dequeue=__on_cmd_dequeue,
            on_cmd_send=__on_cmd_send,
            on_cmd_sent=None,
            on_queue_refill=__on_queue_refill,
            on_queue_empty=__on_queue_empty
        )
        self._marlin.bind_queue(self._job_planner_bind)

    def stop(self):
        if not self._is_running:
            return
        
        self._marlin.unbind_queue()
        self._is_running = False
        self._job_status = status.STOP
        self._job_planner_itor_stopped = True
        self._job_planner_itor = None
        self._job_planner = None
        self._job_progress = 0.0

        self._manager.log_info("Job stopped successfully")

    def pause(self):
        if not self._is_running:
            raise ValueError("job is not running")

        self._marlin.unbind_queue()
        self._job_status = status.PAUSED
        self._manager.log_info("Job paused successfully")

    def swap_working_area(self):
        start = self._job_config.job["job"]["working_area"]["start"]
        end = self._job_config.job["job"]["working_area"]["end"]
        self._job_config.job["job"]["working_area"]["start"] = end
        self._job_config.job["job"]["working_area"]["end"] = start
        self._manager.log_info(f"Swapped working area: start={start}, end={end}")

    def home(self):
        if self._is_running:
            raise ValueError("job is running")
        return self._marlin.write_cmd("G28")
    
    def go_scan_start(self):
        if self._is_running:
            raise ValueError("job is running")
        start = self._job_config.job["job"]["working_area"]["start"]
        return self._marlin.write_cmd(f"G0 X{start[0]} Y{start[1]} Z{start[2]}")

    def go_scan_end(self):
        if self._is_running:
            raise ValueError("job is running")
        end = self._job_config.job["job"]["working_area"]["end"]
        return self._marlin.write_cmd(f"G0 X{end[0]} Y{end[1]} Z{end[2]}")

    def set_scan_start(self):
        if self._is_running:
            raise ValueError("job is running")
        
        position = self._marlin.position
        self._job_config.job["job"]["working_area"]["start"] = [
            position.x_um,
            position.y_um,
            position.z_um
        ]
        return True
    
    def set_scan_end(self):
        if self._is_running:
            raise ValueError("job is running")
        
        position = self._marlin.position
        self._job_config.job["job"]["working_area"]["end"] = [
            position.x_um,
            position.y_um,
            position.z_um
        ]
        return True
    
    def load_config(self, inherit:config):
        self._job_config = inherit
        self._job_config.load_overlay(self._job_dir+"/" + self._current_job, item={"job"})

    def save_config(self):
        if self._current_job is None:
            raise ValueError("No job is currently loaded")
        self._job_config.dump(self._job_dir+"/" + self._current_job, item={"job"})

    # def set_scan_start_position(self, x, y, z):
    #     if self._current_job is None:
    #         raise ValueError("Job configuration is not loaded")
    #     self._job_config.job["job"]["working_area"]["start"] = [x, y, z]

    # def set_scan_end_position(self, x, y, z):
    #     if self._current_job is None:
    #         raise ValueError("Job configuration is not loaded")
    #     self._job_config.job["job"]["working_area"]["end"] = [x, y, z]

    def set_scan_mode(self, mode: str):
        if(mode is None): return
        self._job_config.job["job"]["scan_mode"] = mode

    def get_scan_mode(self):
        return self._job_config.job["job"]["scan_mode"]

    def set_first_axis(self, axis: str):
        if(axis is None): return
        self._job_config.job["job"]["first_axis"] = axis

    def get_first_axis(self):
        return self._job_config.job["job"]["first_axis"]

    def set_planner_path(self, x: str, y:str, z:str):
        if x is not None:
            self._job_config.job["job"]["planner_path"]["x"] = x
        if y is not None:
            self._job_config.job["job"]["planner_path"]["y"] = y
        if z is not None:
            self._job_config.job["job"]["planner_path"]["z"] = z

    def get_planner_path(self, axis: str):
        return self._job_config.job["job"]["planner_path"][axis]

    def get_scan_step_unit(self, axis) -> str:
        if(axis is None): return None
        if(axis not in ["x", "y", "z"]):
            raise ValueError("Invalid axis: " + axis)
        return self._job_config.job["job"]["step_unit"][axis]

    def set_scan_step_unit(self, x: str, y: str, z: str):
        if x not in ["um", "mm", "cm"]:
            raise ValueError("Invalid step unit: " + x)
        if y not in ["um", "mm", "cm"]:
            raise ValueError("Invalid step unit: " + y)
        if z not in ["um", "mm", "cm"]:
            raise ValueError("Invalid step unit: " + z)

        self._job_config.job["job"]["step_unit"]["x"] = x
        self._job_config.job["job"]["step_unit"]["y"] = y
        self._job_config.job["job"]["step_unit"]["z"] = z

    def set_scan_step(self, x_step: int, y_step: int, z_step: int):

        def __get_unit_of_factor(axis) -> int:
            if axis is None: return 1
            unit = self.get_scan_step_unit(axis)
            if unit == "um":
                return 1
            elif unit == "mm":
                return 1000
            elif unit == "cm":
                return 10000
            else:
                raise ValueError("Invalid step unit: " + unit)

        if x_step is not None:
            factor = __get_unit_of_factor("x")
            self._job_config.job["job"]["step_distance"]["x"] = int(x_step * factor)
        if y_step is not None:
            factor = __get_unit_of_factor("y")
            self._job_config.job["job"]["step_distance"]["y"] = int(y_step * factor)
        if z_step is not None:
            factor = __get_unit_of_factor("z")
            self._job_config.job["job"]["step_distance"]["z"] = int(z_step * factor)

    def get_scan_step(self, axis: str):
        if(axis is None): return None
        if(axis not in ["x", "y", "z"]):
            raise ValueError("Invalid axis: " + axis)
        return self._job_config.job["job"]["step_distance"][axis]

    def set_job_repeat(self, repeat: int):
        if(repeat is None or repeat < 1):
            return
        self._job_config.job["job"]["repeat"] = repeat

    def get_job_repeat(self):
        return self._job_config.job["job"]["repeat"]

    def set_repeat(self, repeat: int):
        if(repeat is None or repeat < 1):
            return
        self._job_config.job["job"]["trig_attr"]["repeat"] = repeat

    def get_repeat(self):
        return self._job_config.job["job"]["trig_attr"]["repeat"]

    def set_film_post_delay(self, delay: int):
        if(delay is None or delay < 0):
            return
        self._job_config.job["job"]["trig_attr"]["post_delay_ms"] = delay

    def get_film_post_delay(self):
        return self._job_config.job["job"]["trig_attr"]["post_delay_ms"]

    def set_film_after_delay(self, delay: int):
        if(delay is None or delay < 0):
            return
        self._job_config.job["job"]["trig_attr"]["after_delay_ms"] = delay

    def get_film_after_delay(self):
        return self._job_config.job["job"]["trig_attr"]["after_delay_ms"]

    def set_film_duration(self, duration: int):
        if(duration is None or duration < 0):
            return
        self._job_config.job["job"]["trig_attr"]["active_duration_ms"] = duration

    def get_film_duration(self):
        return self._job_config.job["job"]["trig_attr"]["active_duration_ms"]
